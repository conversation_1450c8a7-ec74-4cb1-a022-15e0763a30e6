import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/ocr_service.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/services/recent_data_service.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class ExpenseFormScreen extends StatefulWidget {
  // Constants for map keys to avoid typos and improve maintainability
  static const String keyInitialDefaultIrsCategory = 'irs_category';

  // Constants for voice processing results
  static const String keyVoiceResultDescription = 'description';
  static const String keyVoiceResultAmount = 'amount';
  static const String keyVoiceResultDate = 'date';
  static const String keyVoiceResultJobName = 'jobName';
  static const String keyVoiceResultTranscribedText = 'transcribedText';
  static const String keyVoiceResultFilePath = 'filePath';
  static const String keyOcrResultMessage = 'message';
  final Expense? expense; // Null for new expense, non-null for editing
  final String? jobId; // Optional job ID when creating from job screen
  final String? initialJobId; // For quick-add with job selection
  final Map<String, dynamic>?
  initialDefaults; // Smart defaults from recent entries
  final bool isOverheadExpense; // For forcing overhead expense creation

  const ExpenseFormScreen({
    super.key,
    this.expense,
    this.jobId,
    this.initialJobId,
    this.initialDefaults,
    this.isOverheadExpense = false,
  });

  @override
  State<ExpenseFormScreen> createState() => _ExpenseFormScreenState();
}

class _ExpenseFormScreenState extends State<ExpenseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _dataRepository = DataRepository();
  final _imagePicker = ImagePicker();
  late final _ocrService = OcrService(
    loadingStateProvider: Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    ),
  );
  final _voiceRecordingService = VoiceRecordingService();
  final _recentDataService = RecentDataService();

  String? _selectedJobId;
  String? _selectedCategory; // IRS Schedule C category
  DateTime _selectedDate = DateTime.now();
  List<Job> _jobs = [];
  List<String> _selectedTags = [];
  File? _receiptImage;
  String? _existingReceiptUrl;
  bool _isOverhead = false;
  String? _voiceNoteUrl;
  List<Map<String, dynamic>> _categoriesByUsage = [];

  bool _isRecording = false;
  String? _errorMessage;
  String? _ocrMessage;

  bool get _isEditing => widget.expense != null;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();
    _loadJobs();
    _setupConnectivityListener();

    // Always load categories by usage for sorting
    _loadCategoriesByUsage();

    // Only load most used category for new expenses without defaults
    if (!_isEditing && widget.initialDefaults == null) {
      _loadMostUsedCategory();
    }

    if (_isEditing) {
      // Populate form fields with existing expense data
      _descriptionController.text = widget.expense!.description;
      _amountController.text = widget.expense!.amount.toString();
      _selectedDate = widget.expense!.date;
      _isOverhead = widget.expense!.isOverhead;
      _selectedJobId = widget.expense!.jobId;
      _existingReceiptUrl = widget.expense!.receiptPhotoUrl;
      _selectedTags = widget.expense!.tags ?? [];
      _selectedCategory = widget.expense!.category;
      _voiceNoteUrl = widget.expense!.voiceNoteUrl;
    } else {
      // Set overhead flag if forced
      if (widget.isOverheadExpense) {
        _isOverhead = true;
        _selectedJobId = null; // Overhead expenses don't have jobs
      }

      if (widget.initialDefaults != null) {
        // Apply smart defaults from recent entries
        if (widget.initialDefaults!.containsKey(
          ExpenseFormScreen.keyInitialDefaultIrsCategory,
        )) {
          _selectedCategory =
              widget.initialDefaults![ExpenseFormScreen
                  .keyInitialDefaultIrsCategory];
        }

        // Set initial job ID from quick-add selection
        if (widget.initialJobId != null) {
          _selectedJobId = widget.initialJobId;
        }
      } else if (widget.jobId != null) {
        _selectedJobId = widget.jobId;
      }
    }
  }

  // Load expense categories sorted by usage frequency
  Future<void> _loadCategoriesByUsage() async {
    try {
      final categories = await _recentDataService.getExpenseCategoriesByUsage();
      if (mounted) {
        setState(() {
          _categoriesByUsage = categories;

          // Log the categories for debugging
          debugPrint('Loaded ${categories.length} categories by usage');
          if (categories.isNotEmpty) {
            final topCategory = categories.first;
            debugPrint(
              'Top category: ${topCategory['category']} with count: ${topCategory['count']}',
            );
          }
        });
      }
    } catch (e) {
      // Log the error but continue with default ordering
      debugPrint('Error loading categories by usage: $e');
    }
  }

  // Load the most frequently used category for this user
  Future<void> _loadMostUsedCategory() async {
    if (_selectedCategory != null) return; // Don't override if already set

    try {
      final mostUsedCategory =
          await _recentDataService.getMostUsedExpenseCategory();
      if (mounted && mostUsedCategory != null) {
        setState(() {
          _selectedCategory = mostUsedCategory;
        });
      }
    } catch (e) {
      // Silently fail, will use default or no category
    }
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _descriptionController.dispose();
    _amountController.dispose();
    _ocrService.dispose(); // Dispose OCR service to free ML Kit resources
    _voiceRecordingService.dispose(); // Dispose voice recording service
    super.dispose();
  }

  Future<void> _loadJobs() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobs',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get jobs from DataRepository (works both online and offline)
          final jobs = await _dataRepository.getJobs();
          final isOnline = await _dataRepository.isOnline();

          setState(() {
            _jobs = jobs;
          });

          // Show connectivity status if offline
          if (!isOnline && mounted) {
            ErrorDisplay.showSync(
              context,
              'You are currently offline. Changes will be synced when connection is restored.',
              isOffline: true,
            );
          }
        },
        message: 'Loading jobs...',
        errorMessage: 'Failed to load jobs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load jobs: ${e.toString()}';
        });
      }
    }
  }

  Future<String?> _uploadReceiptImage() async {
    if (_receiptImage == null) return _existingReceiptUrl;

    // Check if we're online
    final isOnline = await _dataRepository.isOnline();
    if (!isOnline) {
      // If offline, return the local file path
      // The SyncService will handle the actual upload when back online
      return _receiptImage!.path;
    }

    final supabase = Supabase.instance.client;
    final userId = supabase.auth.currentUser?.id;

    if (userId == null) {
      if (mounted) {
        setState(() {
          _errorMessage = 'User not authenticated. Cannot upload receipt.';
        });
      }
      return null;
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final path = '$userId/$timestamp.jpg';

    try {
      await supabase.storage
          .from('receipts')
          .upload(
            path,
            _receiptImage!,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      final receiptUrl = supabase.storage.from('receipts').getPublicUrl(path);
      return receiptUrl;
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'uploadReceiptImage',
            'userId': userId,
            'timestamp': timestamp,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });
      }
      return null;
    }
  }

  Future<void> _pickImage() async {
    await _processPickedImage(ImageSource.gallery);
  }

  Future<void> _takePhoto() async {
    await _processPickedImage(ImageSource.camera);
  }

  Future<void> _processPickedImage(ImageSource source) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );
    if (loadingProvider.isProcessingOcr) return;

    if (mounted) {
      setState(() {
        _ocrMessage = 'Processing receipt...';
        _errorMessage = null; // Clear general errors
      });
    }

    try {
      final pickedFile = await _imagePicker.pickImage(source: source);
      if (pickedFile == null) {
        if (mounted) {
          setState(() {
            _ocrMessage = 'Image selection cancelled.';
          });
        }
        return;
      }

      final imageFile = File(pickedFile.path);
      if (mounted) {
        setState(() {
          _receiptImage = imageFile;
          _existingReceiptUrl =
              null; // Clear existing URL if new image is picked
        });
      }

      // Perform OCR - returns OcrResult which we'll convert to Map<String, dynamic>
      // e.g., {'description': '...', 'amount': '...', 'date': 'YYYY-MM-DD', 'message': '...'}
      final ocrResult = await _ocrService.processReceiptImage(imageFile);

      // Convert OcrResult to Map for consistent handling
      final Map<String, dynamic> ocrData = {
        ExpenseFormScreen.keyVoiceResultDescription: ocrResult.description,
        ExpenseFormScreen.keyVoiceResultAmount: ocrResult.amount,
        ExpenseFormScreen.keyVoiceResultDate: ocrResult.date?.toIso8601String(),
        ExpenseFormScreen.keyOcrResultMessage: 'OCR processing complete.',
      };

      if (mounted) {
        setState(() {
          if (ocrData.containsKey(
                ExpenseFormScreen.keyVoiceResultDescription,
              ) &&
              _descriptionController.text.isEmpty) {
            _descriptionController.text =
                ocrData[ExpenseFormScreen.keyVoiceResultDescription];
          }
          if (ocrData.containsKey(ExpenseFormScreen.keyVoiceResultAmount) &&
              _amountController.text.isEmpty) {
            _amountController.text =
                ocrData[ExpenseFormScreen.keyVoiceResultAmount].toString();
          }
          if (ocrData.containsKey(ExpenseFormScreen.keyVoiceResultDate)) {
            final parsedDate = DateTime.tryParse(
              ocrData[ExpenseFormScreen.keyVoiceResultDate].toString(),
            );
            if (parsedDate != null) {
              _selectedDate = parsedDate;
            }
          }
          final messageFromServer =
              ocrData[ExpenseFormScreen.keyOcrResultMessage];
          _ocrMessage =
              (messageFromServer is String ? messageFromServer : null) ??
              'OCR processing complete.';
        });

        // Show OCR completion feedback
        if (mounted) {
          ErrorDisplay.showOperation(context, FeedbackMessages.ocrCompleted);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _ocrMessage = 'Error processing image: ${e.toString()}';
        });
      }
    }
  }

  void _toggleTag(String tag) {
    setState(() {
      if (_selectedTags.contains(tag)) {
        _selectedTags.remove(tag);
      } else {
        _selectedTags.add(tag);
      }
    });
  }

  Future<void> _startRecording() async {
    if (_isRecording) return;

    setState(() {
      _isRecording = true;
      _errorMessage = null;
    });

    try {
      await _voiceRecordingService.startRecording();
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'startRecording'},
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    setState(() {
      _isRecording = false;
    });

    try {
      final recordingPath = await _voiceRecordingService.stopRecording();

      // Process the recording to extract information
      // First transcribe the audio, then process the transcribed text
      final transcribedText = await _voiceRecordingService.transcribeAudio(
        recordingPath,
      );
      final extractedInfo = _voiceRecordingService.processTranscribedText(
        transcribedText,
      );

      // Update form fields with extracted information
      if (mounted) {
        setState(() {
          if (extractedInfo.containsKey(
                ExpenseFormScreen.keyVoiceResultDescription,
              ) &&
              _descriptionController.text.isEmpty) {
            _descriptionController.text =
                extractedInfo[ExpenseFormScreen.keyVoiceResultDescription];
          }

          if (extractedInfo.containsKey(
                ExpenseFormScreen.keyVoiceResultAmount,
              ) &&
              _amountController.text.isEmpty) {
            _amountController.text =
                extractedInfo[ExpenseFormScreen.keyVoiceResultAmount];
          }

          if (extractedInfo.containsKey(ExpenseFormScreen.keyVoiceResultDate)) {
            final parsedDate = DateTime.tryParse(
              extractedInfo[ExpenseFormScreen.keyVoiceResultDate].toString(),
            );
            if (parsedDate != null) {
              _selectedDate = parsedDate;
            }
          }

          if (extractedInfo.containsKey(
                ExpenseFormScreen.keyVoiceResultJobName,
              ) &&
              !_isOverhead &&
              _jobs.isNotEmpty) {
            final jobName =
                extractedInfo[ExpenseFormScreen.keyVoiceResultJobName]
                    as String;
            Job? foundJob;
            try {
              foundJob = _jobs.firstWhere(
                (job) =>
                    job.title.toLowerCase().contains(jobName.toLowerCase()),
              );
            } catch (e) {
              // No element
              foundJob = _jobs.first; // Fallback to first job
            }
            _selectedJobId = foundJob.id;
          }
        });

        // Show voice operation feedback
        if (mounted) {
          ErrorDisplay.showOperation(
            context,
            'Voice recording processed successfully',
          );
        }
      }

      // Upload the audio file to Supabase Storage
      if (recordingPath.isNotEmpty) {
        final recordId =
            _isEditing && widget.expense != null
                ? widget.expense!.id
                : const Uuid().v4();
        _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
          recordingPath,
          'expenses',
          recordId,
        );
      }
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'processVoiceRecording',
            'isRecording': _isRecording,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveExpense',
        () async {
          if (mounted) {
            setState(() {
              _errorMessage = null;
            });
          }

          final String? receiptUrl = await _uploadReceiptImage();

          // Check if receipt upload failed while online for a new image
          if (_receiptImage != null &&
              receiptUrl == null &&
              (await _dataRepository.isOnline())) {
            if (mounted) {
              final appError = AppError(
                type: ErrorType.storage,
                severity: ErrorSeverity.medium,
                message: 'Failed to process receipt image',
                userFriendlyMessage:
                    'Failed to upload receipt image. Please try again.',
                context: {
                  'operation': 'saveExpense',
                  'hasReceiptImage': true,
                  'isOnline': true,
                },
              );
              ErrorHandler.logError(appError);

              setState(() {
                _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
              });

              ErrorDisplay.showSnackBar(context, appError);
            }
            return;
          }

          final amount = double.parse(_amountController.text);
          final userId = Supabase.instance.client.auth.currentUser!.id;

          if (_isEditing) {
            final updatedExpense = widget.expense!.copyWith(
              description: _descriptionController.text,
              amount: amount,
              date: _selectedDate,
              jobId: _isOverhead ? null : _selectedJobId,
              isOverhead: _isOverhead,
              receiptPhotoUrl:
                  receiptUrl, // Will be local path if offline, or URL
              tags: _selectedTags,
              category: _selectedCategory,
              voiceNoteUrl:
                  _voiceNoteUrl, // Ensure this is updated if a new recording is made
            );
            await _dataRepository.updateExpense(updatedExpense);

            // Update category usage count if category is selected
            if (_selectedCategory != null) {
              await _recentDataService.updateExpenseCategoryUsage(
                _selectedCategory!,
              );

              // Refresh categories by usage to reflect the updated counts
              await _loadCategoriesByUsage();
            }
          } else {
            final newExpense = Expense(
              id: const Uuid().v4(),
              userId: userId,
              description: _descriptionController.text,
              amount: amount,
              date: _selectedDate,
              jobId: _isOverhead ? null : _selectedJobId,
              isOverhead: _isOverhead,
              receiptPhotoUrl:
                  receiptUrl, // Will be local path if offline, or URL
              tags: _selectedTags,
              category: _selectedCategory,
              voiceNoteUrl: _voiceNoteUrl,
              createdAt: DateTime.now(),
            );
            await _dataRepository.addExpense(newExpense);

            // Update category usage count if category is selected
            if (_selectedCategory != null) {
              await _recentDataService.updateExpenseCategoryUsage(
                _selectedCategory!,
              );

              // Refresh categories by usage to reflect the updated counts
              await _loadCategoriesByUsage();
            }
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'expense',
              operation,
              isOffline: _isOffline,
            );
            Navigator.of(
              context,
            ).pop(true); // Pop with a result to indicate success
          }
        },
        message: _isEditing ? 'Updating expense...' : 'Creating expense...',
        errorMessage: 'Failed to save expense',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveExpense',
            'isEditing': _isEditing,
            'isOverhead': _isOverhead,
            'hasReceiptImage': _receiptImage != null,
            'selectedJobId': _selectedJobId,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Expense' : 'New Expense'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Tooltip(
                message:
                    'You are currently offline. Changes will be saved locally.',
                child: const Icon(Icons.cloud_off, color: Colors.white),
              ),
            ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          if (loadingProvider.isLoading('loadJobs')) {
            return const Center(
              child: QuarterliesLoadingIndicator(
                message: 'Loading jobs...',
                size: 32.0,
              ),
            );
          }

          return Consumer<DisplaySettingsProvider>(
            builder: (context, displayProvider, child) {
              return SingleChildScrollView(
                padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12 : 16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_errorMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              border: Border.all(color: Colors.red.shade200),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.red.shade700,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red.shade700,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.close),
                                  iconSize: 18,
                                  color: Colors.red.shade700,
                                  onPressed: () {
                                    setState(() {
                                      _errorMessage = null;
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),

                      // Offline status indicator
                      if (_isOffline)
                        Container(
                          padding: const EdgeInsets.all(8),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.2),
                            borderRadius: const BorderRadius.all(
                              Radius.circular(8.0),
                            ),
                            border: Border.all(color: Colors.orange),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.cloud_off, color: Colors.orange),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'You are currently offline. Your changes will be saved locally and synced when connection is restored.',
                                  style: TextStyle(
                                    color: Colors.orange.shade800,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Basic Expense Information Section
                      AdaptiveFormSection(
                        title: 'Basic Information',
                        icon: Icons.receipt_long,
                        children: [
                          // Overhead expense checkbox
                          CheckboxListTile(
                            title: Text(
                              'Overhead Expense (not tied to a specific job)',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 13 : 14,
                              ),
                            ),
                            value: _isOverhead,
                            onChanged: (value) {
                              setState(() {
                                _isOverhead = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                          ),

                          // Job selection dropdown (only shown if not an overhead expense)
                          if (!_isOverhead)
                            DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                labelText: 'Job',
                                border: OutlineInputBorder(
                                  borderRadius: const BorderRadius.all(
                                    Radius.circular(8.0),
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                              value: _selectedJobId,
                              items:
                                  _jobs.map((job) {
                                    return DropdownMenuItem<String>(
                                      value: job.id,
                                      child: Text(job.title),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedJobId = value;
                                });
                              },
                              validator: (value) {
                                if (!_isOverhead && value == null) {
                                  return 'Please select a job';
                                }
                                return null;
                              },
                            ),
                        ],
                      ),

                      // Expense Details Section
                      AdaptiveFormSection(
                        title: 'Expense Details',
                        icon: Icons.edit,
                        children: [
                          if (displayProvider.isOfficeMode) ...[
                            // Office Mode: Description and Amount in same row
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: CustomTextField(
                                    controller: _descriptionController,
                                    labelText: 'Description',
                                    hintText: 'Enter expense description',
                                    validator:
                                        (value) =>
                                            InputValidators.validateRequired(
                                              value,
                                              'Description',
                                            ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: CustomTextField(
                                    controller: _amountController,
                                    labelText: 'Amount',
                                    hintText: 'Enter expense amount',
                                    keyboardType: TextInputType.number,
                                    validator:
                                        (value) =>
                                            InputValidators.validateCurrency(
                                              value,
                                              required: true,
                                              fieldName: 'Amount',
                                            ),
                                  ),
                                ),
                              ],
                            ),
                          ] else ...[
                            // Field Mode: Separate fields
                            CustomTextField(
                              controller: _descriptionController,
                              labelText: 'Description',
                              hintText: 'Enter expense description',
                              validator:
                                  (value) => InputValidators.validateRequired(
                                    value,
                                    'Description',
                                  ),
                            ),
                            CustomTextField(
                              controller: _amountController,
                              labelText: 'Amount',
                              hintText: 'Enter expense amount',
                              keyboardType: TextInputType.number,
                              validator:
                                  (value) => InputValidators.validateCurrency(
                                    value,
                                    required: true,
                                    fieldName: 'Amount',
                                  ),
                            ),
                          ],

                          // Date picker
                          InkWell(
                            onTap: () async {
                              final pickedDate = await showDatePicker(
                                context: context,
                                initialDate: _selectedDate,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  _selectedDate = pickedDate;
                                });
                              }
                            },
                            child: InputDecorator(
                              decoration: InputDecoration(
                                labelText: 'Date',
                                border: OutlineInputBorder(
                                  borderRadius: const BorderRadius.all(
                                    Radius.circular(8.0),
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey[100],
                              ),
                              child: Text(
                                DateFormat('MM/dd/yyyy').format(_selectedDate),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Category and Tags Section
                      AdaptiveFormSection(
                        title: 'Category & Tags',
                        icon: Icons.category,
                        children: [
                          // IRS Schedule C Category dropdown
                          DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'Expense Category (Schedule C)',
                              border: OutlineInputBorder(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.grey[100],
                              helperText:
                                  displayProvider.isOfficeMode
                                      ? 'Sorted by usage frequency'
                                      : 'Categories are sorted by your most frequently used',
                              prefixIcon: const Icon(Icons.category),
                              suffixIcon:
                                  _categoriesByUsage.isNotEmpty
                                      ? Tooltip(
                                        message:
                                            'Categories are sorted by your usage frequency',
                                        child: Icon(
                                          Icons.info_outline,
                                          color: Colors.blue.shade400,
                                          size: 16,
                                        ),
                                      )
                                      : null,
                            ),
                            value: _selectedCategory,
                            items: _buildCategoryDropdownItems(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value;
                              });
                            },
                            isExpanded: true,
                            validator:
                                (value) => InputValidators.validateRequired(
                                  value,
                                  'Category',
                                ),
                          ),

                          // Tags section
                          Padding(
                            padding: EdgeInsets.only(
                              top: displayProvider.isOfficeMode ? 12 : 16,
                              bottom: displayProvider.isOfficeMode ? 6 : 8,
                            ),
                            child: Text(
                              'Tags',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 14 : 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Wrap(
                            spacing: displayProvider.isOfficeMode ? 6 : 8,
                            runSpacing: displayProvider.isOfficeMode ? 4 : 6,
                            children: [
                              _buildTagChip('Materials'),
                              _buildTagChip('Tools'),
                              _buildTagChip('Subcontractor'),
                              _buildTagChip('Travel'),
                              _buildTagChip('Office'),
                              _buildTagChip('Permits'),
                            ],
                          ),
                        ],
                      ),

                      // Receipt Photo Section
                      AdaptiveFormSection(
                        title: 'Receipt Photo',
                        icon: Icons.receipt,
                        children: [
                          // Voice recording button
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Add Receipt Photo',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              IconButton(
                                icon: Icon(
                                  _isRecording ? Icons.stop : Icons.mic,
                                ),
                                onPressed:
                                    _isRecording
                                        ? _stopRecording
                                        : _startRecording,
                                tooltip:
                                    _isRecording
                                        ? 'Stop Recording'
                                        : 'Record Voice Note',
                                color: _isRecording ? Colors.red : null,
                              ),
                            ],
                          ),

                          // Voice recording status
                          if (_isRecording)
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: const VoiceLoadingIndicator(
                                operation: 'Recording voice note...',
                                isRecording: true,
                              ),
                            ),
                          if (displayProvider.isOfficeMode) ...[
                            // Office Mode: Compact button layout
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(
                                      Icons.photo_library,
                                      size: 18,
                                    ),
                                    label: const Text('Gallery'),
                                    onPressed: () {
                                      final loadingProvider =
                                          Provider.of<LoadingStateProvider>(
                                            context,
                                            listen: false,
                                          );
                                      if (!loadingProvider.isProcessingOcr) {
                                        _pickImage();
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(
                                      Icons.camera_alt,
                                      size: 18,
                                    ),
                                    label: const Text('Camera'),
                                    onPressed: () {
                                      final loadingProvider =
                                          Provider.of<LoadingStateProvider>(
                                            context,
                                            listen: false,
                                          );
                                      if (!loadingProvider.isProcessingOcr) {
                                        _takePhoto();
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ] else ...[
                            // Field Mode: Larger buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.photo_library),
                                  label: const Text('Gallery'),
                                  onPressed: () {
                                    final loadingProvider =
                                        Provider.of<LoadingStateProvider>(
                                          context,
                                          listen: false,
                                        );
                                    if (!loadingProvider.isProcessingOcr) {
                                      _pickImage();
                                    }
                                  },
                                ),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.camera_alt),
                                  label: const Text('Camera'),
                                  onPressed: () {
                                    final loadingProvider =
                                        Provider.of<LoadingStateProvider>(
                                          context,
                                          listen: false,
                                        );
                                    if (!loadingProvider.isProcessingOcr) {
                                      _takePhoto();
                                    }
                                  },
                                ),
                              ],
                            ),
                          ],

                          // Processing and status messages
                          Consumer<LoadingStateProvider>(
                            builder: (context, loadingProvider, child) {
                              if (loadingProvider.isProcessingOcr) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: OcrLoadingIndicator(
                                    stage:
                                        loadingProvider.ocrStage ??
                                        'Processing receipt...',
                                    progress: loadingProvider.ocrProgress,
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          Consumer<LoadingStateProvider>(
                            builder: (context, loadingProvider, child) {
                              if (_ocrMessage != null &&
                                  !loadingProvider.isProcessingOcr) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: Text(
                                    _ocrMessage!,
                                    style: TextStyle(
                                      color:
                                          _ocrMessage!.toLowerCase().contains(
                                                    'successfully',
                                                  ) ||
                                                  _ocrMessage!
                                                      .toLowerCase()
                                                      .contains('complete')
                                              ? Colors.green
                                              : (_ocrMessage!
                                                      .toLowerCase()
                                                      .contains('error') ||
                                                  _ocrMessage!
                                                      .toLowerCase()
                                                      .contains('failed'))
                                              ? Colors.red
                                              : Theme.of(
                                                context,
                                              ).textTheme.bodySmall?.color,
                                    ),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          const SizedBox(height: 16),

                          // Preview of selected image or existing receipt
                          if (_receiptImage != null)
                            Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                                child: Image.file(
                                  _receiptImage!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          else if (_existingReceiptUrl != null)
                            Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                                child: Image.network(
                                  _existingReceiptUrl!,
                                  fit: BoxFit.cover,
                                  loadingBuilder: (
                                    context,
                                    child,
                                    loadingProgress,
                                  ) {
                                    if (loadingProgress == null) {
                                      return child;
                                    }
                                    return const Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Text('Failed to load image'),
                                    );
                                  },
                                ),
                              ),
                            ),

                          // Preview of selected image or existing receipt
                          if (_receiptImage != null)
                            Container(
                              height: displayProvider.isOfficeMode ? 150 : 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                                child: Image.file(
                                  _receiptImage!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          else if (_existingReceiptUrl != null)
                            Container(
                              height: displayProvider.isOfficeMode ? 150 : 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(8.0),
                                ),
                                child: Image.network(
                                  _existingReceiptUrl!,
                                  fit: BoxFit.cover,
                                  loadingBuilder: (
                                    context,
                                    child,
                                    loadingProgress,
                                  ) {
                                    if (loadingProgress == null) {
                                      return child;
                                    }
                                    return const Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                      child: Text('Failed to load image'),
                                    );
                                  },
                                ),
                              ),
                            ),
                        ],
                      ),

                      // Save button
                      Consumer<LoadingStateProvider>(
                        builder: (context, loadingProvider, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed:
                                  loadingProvider.isLoading('saveExpense')
                                      ? null
                                      : _saveExpense,
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 12 : 16,
                                ),
                              ),
                              child:
                                  loadingProvider.isLoading('saveExpense')
                                      ? Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2.0,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            _isEditing
                                                ? 'Updating...'
                                                : 'Saving...',
                                            style: TextStyle(
                                              fontSize:
                                                  displayProvider.isOfficeMode
                                                      ? 14
                                                      : 16,
                                            ),
                                          ),
                                        ],
                                      )
                                      : Text(
                                        _isEditing
                                            ? 'Update Expense'
                                            : 'Save Expense',
                                        style: TextStyle(
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 14
                                                  : 16,
                                        ),
                                      ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildTagChip(String tag) {
    final isSelected = _selectedTags.contains(tag);
    return FilterChip(
      label: Text(tag),
      selected: isSelected,
      onSelected: (_) => _toggleTag(tag),
      backgroundColor: Colors.grey[200],
      selectedColor: Theme.of(
        context,
      ).colorScheme.primary.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).colorScheme.primary,
    );
  }

  // Build category dropdown items sorted by usage frequency
  List<DropdownMenuItem<String>> _buildCategoryDropdownItems() {
    // Start with a "Select a category..." option
    final items = <DropdownMenuItem<String>>[
      const DropdownMenuItem<String>(
        value: null,
        child: Text('Select a category...'),
      ),
    ];

    // If we have usage data, sort categories by usage
    if (_categoriesByUsage.isNotEmpty) {
      // Create a set of categories that have usage data
      final usedCategoriesSet =
          _categoriesByUsage.map((item) => item['category'] as String).toSet();

      // Sort the categories by usage count (descending)
      final sortedCategories = List<Map<String, dynamic>>.from(
        _categoriesByUsage,
      );
      sortedCategories.sort(
        (a, b) => (b['count'] as int).compareTo(a['count'] as int),
      );

      // Add a header for frequently used categories
      if (sortedCategories.isNotEmpty) {
        items.add(
          DropdownMenuItem<String>(
            enabled: false,
            value: 'frequently_used_header',
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.star, size: 16, color: Colors.amber.shade700),
                  const SizedBox(width: 8),
                  const Text(
                    'FREQUENTLY USED',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }

      // Add categories with usage data first, sorted by frequency
      for (final item in sortedCategories) {
        final category = item['category'] as String;
        final count = item['count'] as int;

        // Calculate a color intensity based on usage frequency
        // More frequently used categories will have a more intense background color
        final maxCount =
            sortedCategories.isNotEmpty
                ? (sortedCategories.first['count'] as int)
                : 1;
        final intensity = (count / maxCount).clamp(0.1, 1.0);
        final backgroundColor = Color.lerp(
          Colors.white,
          Colors.blue.shade100,
          intensity,
        );

        items.add(
          DropdownMenuItem<String>(
            value: category,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 25),
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.category, size: 16, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      category,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: Colors.blue.shade200,
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      '$count',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }

      // Add remaining categories that don't have usage data
      final unusedCategories =
          ExpenseCategory.values
              .where((category) => !usedCategoriesSet.contains(category))
              .toList();

      // Sort unused categories alphabetically
      unusedCategories.sort((a, b) => a.compareTo(b));

      // Add a header for other categories
      if (unusedCategories.isNotEmpty) {
        items.add(
          DropdownMenuItem<String>(
            enabled: false,
            value: 'other_categories_header',
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.list, size: 16, color: Colors.grey.shade700),
                  const SizedBox(width: 8),
                  const Text(
                    'OTHER CATEGORIES',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }

      for (final category in unusedCategories) {
        items.add(
          DropdownMenuItem<String>(
            value: category,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: Row(
                children: [
                  const Icon(
                    Icons.category_outlined,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  Text(category, style: const TextStyle(color: Colors.grey)),
                ],
              ),
            ),
          ),
        );
      }
    } else {
      // If no usage data, sort alphabetically
      final sortedCategories =
          ExpenseCategory.values.toList()..sort((a, b) => a.compareTo(b));
      for (final category in sortedCategories) {
        items.add(
          DropdownMenuItem<String>(
            value: category,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: Row(
                children: [
                  const Icon(Icons.category, size: 16, color: Colors.black54),
                  const SizedBox(width: 8),
                  Text(category),
                ],
              ),
            ),
          ),
        );
      }
    }

    return items;
  }
}
