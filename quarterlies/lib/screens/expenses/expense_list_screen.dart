import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/expenses/expense_detail_screen.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

class ExpenseListScreen extends StatefulWidget {
  final String? jobId; // Optional job ID to filter expenses

  const ExpenseListScreen({super.key, this.jobId});

  @override
  State<ExpenseListScreen> createState() => _ExpenseListScreenState();
}

class _ExpenseListScreenState extends State<ExpenseListScreen> {
  final DataRepository _dataRepository = DataRepository();
  List<Expense> _expenses = [];
  List<Expense> _filteredExpenses = [];
  List<Job> _jobs = [];
  String? _selectedJobId;
  String? _selectedCategory; // Changed from _selectedType to _selectedCategory
  String? _selectedExpenseType; // New filter for overhead vs job expenses
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _selectedJobId = widget.jobId;
    _loadData();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    await _dataRepository.isOnline();

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadExpenseData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs for filtering
          final jobs = await _dataRepository.getJobs();

          // Load expenses based on job ID filter
          final expenses =
              _selectedJobId != null
                  ? await _dataRepository.getExpensesByJob(_selectedJobId!)
                  : await _dataRepository.getExpenses();

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _expenses = expenses;
            _applyFilters();
          });
        },
        message: 'Loading expenses...',
        errorMessage: 'Failed to load expenses',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load expenses: ${e.toString()}';
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredExpenses =
          _expenses.where((expense) {
            // Apply job filter if selected
            if (_selectedJobId != null && expense.jobId != _selectedJobId) {
              return false;
            }

            // Apply category filter if selected (instead of type)
            if (_selectedCategory != null &&
                expense.category != _selectedCategory) {
              return false;
            }

            // Apply expense type filter (overhead vs job expenses)
            if (_selectedExpenseType != null) {
              if (_selectedExpenseType == 'overhead' && !expense.isOverhead) {
                return false;
              }
              if (_selectedExpenseType == 'job' && expense.isOverhead) {
                return false;
              }
            }

            return true;
          }).toList();
    });
  }

  String _getJobTitle(String? jobId) {
    if (jobId == null) return 'Overhead Expense';

    final job = _jobs.firstWhere(
      (job) => job.id == jobId,
      orElse:
          () => Job(id: '', userId: '', customerId: '', title: 'Unknown Job'),
    );
    return job.title;
  }

  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey[300]!;

    // Use different colors for different categories
    // This is just an example, you can adjust the colors as needed
    switch (category) {
      case ExpenseCategory.advertising:
        return Colors.blue[100]!;
      case ExpenseCategory.carAndTruck:
        return Colors.green[100]!;
      case ExpenseCategory.contractLabor:
        return Colors.orange[100]!;
      case ExpenseCategory.officeExpense:
        return Colors.purple[100]!;
      case ExpenseCategory.legalAndProfessional:
        return Colors.red[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.jobId != null ? 'Job Expenses' : 'All Expenses'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadExpenseData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading expenses...',
                  size: 32.0,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _filteredExpenses.isEmpty
              ? const Center(child: Text('No expenses found'))
              : RefreshIndicator(
                onRefresh: _loadData,
                child: ListView.builder(
                  itemCount: _filteredExpenses.length,
                  itemBuilder: (context, index) {
                    final expense = _filteredExpenses[index];
                    return Consumer<DisplaySettingsProvider>(
                      builder: (context, displayProvider, child) {
                        return AdaptiveListTile(
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  expense.description,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                              if (expense.isOverhead)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.orange[100],
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.orange[300]!,
                                    ),
                                  ),
                                  child: Text(
                                    'OVERHEAD',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.orange[800],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          subtitle: Text(
                            '\$${expense.amount.toStringAsFixed(2)} • ${DateFormat('MM/dd/yyyy').format(expense.date)}${expense.category != null ? ' • ${expense.category}' : ''}',
                          ),
                          leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  expense.isOverhead
                                      ? Colors.orange
                                      : Colors.green,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              expense.isOverhead
                                  ? Icons.business
                                  : Icons.receipt,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          trailing: Chip(
                            label: Text(
                              expense.category ?? 'Uncategorized',
                              style: const TextStyle(fontSize: 12),
                            ),
                            backgroundColor: _getCategoryColor(
                              expense.category,
                            ),
                          ),
                          // Additional info shown only in Office Mode
                          additionalInfo:
                              displayProvider.isOfficeMode
                                  ? OfficeAdditionalInfo(
                                    items: [
                                      if (expense.jobId != null &&
                                          !expense.isOverhead)
                                        InfoItem(
                                          label: 'Job',
                                          value: _getJobTitle(expense.jobId),
                                          icon: Icons.work,
                                        ),
                                      if (expense.tags != null &&
                                          expense.tags!.isNotEmpty)
                                        InfoItem(
                                          label: 'Tags',
                                          value: expense.tags!.join(', '),
                                          icon: Icons.tag,
                                        ),
                                      if (expense.receiptPhotoUrl != null)
                                        const InfoItem(
                                          label: 'Receipt',
                                          value: 'Photo attached',
                                          icon: Icons.photo,
                                        ),
                                    ],
                                  )
                                  : null,
                          // Office actions shown only in Office Mode
                          officeActions:
                              displayProvider.isOfficeMode
                                  ? [
                                    OfficeActionButton(
                                      icon: Icons.edit,
                                      label: 'Edit',
                                      onPressed: () async {
                                        final result = await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) => ExpenseFormScreen(
                                                  expense: expense,
                                                  jobId: widget.jobId,
                                                ),
                                          ),
                                        );
                                        if (result == true) {
                                          _loadData();
                                        }
                                      },
                                    ),
                                    if (expense.receiptPhotoUrl != null)
                                      OfficeActionButton(
                                        icon: Icons.photo,
                                        label: 'Receipt',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Receipt viewer coming soon!',
                                          );
                                        },
                                      ),
                                    OfficeActionButton(
                                      icon: Icons.copy,
                                      label: 'Duplicate',
                                      onPressed: () {
                                        ErrorDisplay.showInfo(
                                          context,
                                          'Duplicate functionality coming soon!',
                                        );
                                      },
                                    ),
                                  ]
                                  : null,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => ExpenseDetailScreen(
                                      expenseId: expense.id,
                                    ),
                              ),
                            ).then((_) => _loadData());
                          },
                        );
                      },
                    );
                  },
                ),
              );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ExpenseFormScreen(jobId: widget.jobId),
            ),
          ).then((_) => _loadData());
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Expenses'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Job filter dropdown
                  if (widget.jobId == null) ...[
                    const Text('Job'),
                    DropdownButton<String?>(
                      isExpanded: true,
                      value: _selectedJobId,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('All Jobs'),
                        ),
                        ..._jobs.map((job) {
                          return DropdownMenuItem<String?>(
                            value: job.id,
                            child: Text(job.title),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedJobId = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Expense type filter dropdown (overhead vs job expenses)
                  const Text('Expense Type'),
                  DropdownButton<String?>(
                    isExpanded: true,
                    value: _selectedExpenseType,
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Expenses'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'job',
                        child: Text('Job Expenses'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'overhead',
                        child: Text('Overhead Expenses'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedExpenseType = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Category filter dropdown
                  const Text('Expense Category'),
                  DropdownButton<String?>(
                    isExpanded: true,
                    value: _selectedCategory,
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Categories'),
                      ),
                      ...ExpenseCategory.values.map((category) {
                        return DropdownMenuItem<String?>(
                          value: category,
                          child: Text(category),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
