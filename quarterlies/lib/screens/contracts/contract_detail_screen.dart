import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/contract_pdf_service.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/services/document_signature_service.dart';
import 'package:quarterlies/screens/document_viewer_screen.dart';
import 'package:quarterlies/screens/document_signing/create_signing_request_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_form_screen.dart';
import 'package:quarterlies/screens/contracts/contract_form_screen.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:share_plus/share_plus.dart';

class ContractDetailScreen extends StatefulWidget {
  final String contractId;

  const ContractDetailScreen({super.key, required this.contractId});

  @override
  State<ContractDetailScreen> createState() => _ContractDetailScreenState();
}

class _ContractDetailScreenState extends State<ContractDetailScreen> {
  final DataRepository _dataRepository = DataRepository();
  final ContractPdfService _contractPdfService = ContractPdfService();
  final DocumentSigningService _documentSigningService =
      DocumentSigningService();

  Contract? _contract;
  Customer? _customer;
  Job? _job;
  String? _errorMessage;
  bool _isDocumentSigned = false;

  @override
  void initState() {
    super.initState();
    _loadContractData();
  }

  Future<void> _checkIfDocumentSigned() async {
    try {
      final isSigned = await _documentSigningService.isDocumentSigned(
        widget.contractId,
        'contract',
      );

      if (mounted) {
        setState(() {
          _isDocumentSigned = isSigned;
        });
      }
    } catch (e) {
      debugPrint('Error checking if document is signed: $e');
    }
  }

  Future<void> _loadContractData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadContractData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load contract data
          final contract = await _dataRepository.getContractById(
            widget.contractId,
          );
          if (contract == null) {
            setState(() {
              _errorMessage = 'Contract not found';
            });
            return;
          }

          // Load related customer and job
          final customer = await _dataRepository.getCustomerById(
            contract.customerId,
          );
          final job = await _dataRepository.getJobById(contract.jobId);

          if (!mounted) return;

          setState(() {
            _contract = contract;
            _customer = customer;
            _job = job;
          });

          // Check if the document is signed
          await _checkIfDocumentSigned();
        },
        message: 'Loading contract data...',
        errorMessage: 'Failed to load contract data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load contract data: ${e.toString()}';
        });
      }
    }
  }

  /// Update contract status
  Future<void> _updateContractStatus(String status) async {
    try {
      final updatedContract = _contract!.copyWith(status: status);
      await _dataRepository.updateContract(updatedContract);

      _loadContractData();

      if (mounted) {
        ErrorDisplay.showSuccess(context, 'Contract status updated to $status');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update contract: ${e.toString()}',
        );
      }
    }
  }

  /// View contract as PDF
  Future<void> _viewContractPdf() async {
    // Validate required data is available
    if (_contract == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot generate PDF: Missing contract data',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _contractPdfService.generateContractPdf(
          contract: _contract!,
          customer: _customer!,
          job: _job!,
        );

        if (!mounted) return;

        // Show success feedback
        ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);

        // Open the PDF in the document viewer
        final contractNumber = _contract!.id.substring(0, 8);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => DocumentViewerScreen(
                  title: 'Contract #$contractNumber',
                  documentBytes: pdfBytes,
                ),
          ),
        );
      }, documentType: 'Contract');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }

  /// Generate and share contract as PDF
  Future<void> _generateAndSharePdf() async {
    // Validate required data is available
    if (_contract == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot generate PDF: Missing contract data',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _contractPdfService.generateContractPdf(
          contract: _contract!,
          customer: _customer!,
          job: _job!,
        );

        if (!mounted) return;

        // Handle sharing based on platform
        if (kIsWeb) {
          // Web platform - offer download via browser
          try {
            // Show success message for web platform
            ErrorDisplay.showOperation(
              context,
              'PDF generated successfully. Download would be available in production.',
            );
          } catch (webError) {
            if (!mounted) return;
            ErrorDisplay.showWarning(
              context,
              'Error in web environment: ${webError.toString()}',
            );
          }
        } else {
          // Mobile platforms - save file and share
          try {
            // Create a descriptive filename
            final contractNumber = _contract!.id.substring(0, 8);
            final customerName = _customer!.name.replaceAll(' ', '_');
            final fileName = 'contract_${contractNumber}_$customerName.pdf';

            // Save PDF to temporary file
            final file = await _contractPdfService.savePdfToFile(
              pdfBytes,
              fileName,
            );

            // Share the PDF file
            await SharePlus.instance.share(
              ShareParams(
                files: [XFile(file.path)],
                text: 'Please find attached the contract for your review.',
                subject: 'Contract #$contractNumber for ${_customer!.name}',
                sharePositionOrigin: const Rect.fromLTWH(0, 0, 10, 10),
              ),
            );

            // Show success feedback
            if (mounted) {
              ErrorDisplay.showOperation(context, 'PDF shared successfully');
            }
          } catch (shareError) {
            if (mounted) {
              ErrorDisplay.showWarning(
                context,
                'Error sharing PDF: ${shareError.toString()}',
              );
            }
          }
        }
      }, documentType: 'Contract');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _contract != null
              ? 'Contract #${_contract!.id.substring(0, 8)}'
              : 'Contract Details',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // View PDF button
          if (_contract != null)
            Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isGeneratingPdf = loadingProvider.isGeneratingPdf;
                return IconButton(
                  icon:
                      isGeneratingPdf
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Icon(Icons.visibility),
                  tooltip: 'View Contract',
                  onPressed: isGeneratingPdf ? null : _viewContractPdf,
                );
              },
            ),
          // PDF download/share button
          if (_contract != null)
            Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isGeneratingPdf = loadingProvider.isGeneratingPdf;
                return IconButton(
                  icon:
                      isGeneratingPdf
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Icon(Icons.share),
                  tooltip: 'Share Contract',
                  onPressed: isGeneratingPdf ? null : _generateAndSharePdf,
                );
              },
            ),
          // Electronic signature button
          if (_contract != null)
            Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isGeneratingPdf = loadingProvider.isGeneratingPdf;
                return IconButton(
                  icon:
                      isGeneratingPdf
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : Icon(
                            Icons.draw,
                            color:
                                _isDocumentSigned ? Colors.green : Colors.white,
                          ),
                  tooltip: 'Request Electronic Signature',
                  onPressed:
                      isGeneratingPdf ? null : _requestElectronicSignature,
                );
              },
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<LoadingStateProvider>(
      builder: (context, loadingProvider, child) {
        final isLoading = loadingProvider.isLoading('loadContractData');

        if (isLoading) {
          return const Center(
            child: QuarterliesLoadingIndicator(
              message: 'Loading contract data...',
              size: 32.0,
            ),
          );
        }

        if (_errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadContractData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (_contract == null || _customer == null || _job == null) {
          return const Center(child: Text('Contract data not available'));
        }

        return Consumer<DisplaySettingsProvider>(
          builder: (context, displayProvider, child) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildContractHeader(displayProvider),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                  _buildContractDetails(displayProvider),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                  _buildLineItems(displayProvider),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                  _buildActionButtons(displayProvider),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildContractHeader(DisplaySettingsProvider displayProvider) {
    return AdaptiveDetailSection(
      title: 'Contract Details',
      icon: Icons.description,
      alwaysExpandedInOffice: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Contract #${_contract!.id.substring(0, 8)}',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 16 : 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Chip(
              label: Text(_contract!.status),
              backgroundColor: _getStatusColor(_contract!.status),
            ),
          ],
        ),
        SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),

        if (displayProvider.isOfficeMode) ...[
          // Office Mode: Compact grid layout
          Row(
            children: [
              Expanded(
                child: AdaptiveInfoRow(
                  label: 'Customer',
                  value: _customer!.name,
                  icon: Icons.person,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdaptiveInfoRow(
                  label: 'Job',
                  value: _job!.title,
                  icon: Icons.work,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          AdaptiveInfoRow(
            label: 'Created',
            value: DateFormat('MM/dd/yyyy').format(_contract!.createdAt),
            icon: Icons.calendar_today,
          ),
          if (_contract!.notes != null && _contract!.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            AdaptiveInfoRow(
              label: 'Notes',
              value: _contract!.notes!,
              icon: Icons.note,
            ),
          ],
        ] else ...[
          // Field Mode: Existing layout
          Text('Customer: ${_customer!.name}'),
          Text('Job: ${_job!.title}'),
          Text(
            'Created: ${DateFormat('MM/dd/yyyy').format(_contract!.createdAt)}',
          ),
          if (_contract!.notes != null && _contract!.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            const Text('Notes:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(_contract!.notes!),
          ],
        ],
      ],
    );
  }

  Widget _buildContractDetails(DisplaySettingsProvider displayProvider) {
    return AdaptiveDetailSection(
      title: 'Customer & Service Information',
      icon: Icons.info,
      alwaysExpandedInOffice: true,
      children: [
        if (displayProvider.isOfficeMode) ...[
          // Office Mode: Compact grid layout
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AdaptiveInfoRow(
                      label: 'Customer',
                      value: _customer!.name,
                      icon: Icons.person,
                    ),
                    if (_customer!.email != null) ...[
                      const SizedBox(height: 4),
                      AdaptiveInfoRow(
                        label: 'Email',
                        value: _customer!.email!,
                        icon: Icons.email,
                      ),
                    ],
                    if (_customer!.phone != null) ...[
                      const SizedBox(height: 4),
                      AdaptiveInfoRow(
                        label: 'Phone',
                        value: _customer!.phone!,
                        icon: Icons.phone,
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_job!.address != null)
                      AdaptiveInfoRow(
                        label: 'Service Address',
                        value: _job!.address!,
                        icon: Icons.location_on,
                      ),
                    if (_job!.city != null &&
                        _job!.state != null &&
                        _job!.zipCode != null) ...[
                      const SizedBox(height: 4),
                      AdaptiveInfoRow(
                        label: 'City, State ZIP',
                        value: '${_job!.city}, ${_job!.state} ${_job!.zipCode}',
                        icon: Icons.place,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ] else ...[
          // Field Mode: Existing layout
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Customer:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(_customer!.name),
                    if (_customer!.email != null)
                      Text('Email: ${_customer!.email}'),
                    if (_customer!.phone != null)
                      Text('Phone: ${_customer!.phone}'),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Service Address:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (_job!.address != null) Text(_job!.address!),
                    if (_job!.city != null &&
                        _job!.state != null &&
                        _job!.zipCode != null)
                      Text('${_job!.city}, ${_job!.state} ${_job!.zipCode}'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLineItems(DisplaySettingsProvider displayProvider) {
    return AdaptiveDetailSection(
      title: 'Line Items',
      icon: Icons.list_alt,
      alwaysExpandedInOffice: true,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _contract!.lineItems.length,
          itemBuilder: (context, index) {
            final item = _contract!.lineItems[index];
            return ListTile(
              contentPadding: EdgeInsets.symmetric(
                horizontal: displayProvider.isOfficeMode ? 8 : 16,
                vertical: displayProvider.isOfficeMode ? 4 : 8,
              ),
              title: Text(
                item.description,
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 14 : 16,
                ),
              ),
              subtitle: Text(
                '${item.quantity} ${item.unit ?? ''} @ \$${item.unitPrice.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                ),
              ),
              trailing: Text(
                '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: displayProvider.isOfficeMode ? 14 : 16,
                ),
              ),
            );
          },
        ),
        const Divider(),
        Align(
          alignment: Alignment.centerRight,
          child: Text(
            'Total: \$${_contract!.totalAmount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(DisplaySettingsProvider displayProvider) {
    return AdaptiveDetailSection(
      title: 'Actions',
      icon: Icons.settings,
      alwaysExpandedInOffice: true,
      children: [
        // Electronic signature status indicator
        if (_isDocumentSigned)
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.verified, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Electronically Signed',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                      const Text(
                        'This contract has been electronically signed by the customer.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        // Status update buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (_contract!.status == ContractStatus.draft)
              ElevatedButton.icon(
                icon: const Icon(Icons.send),
                label: const Text('Mark as Sent'),
                onPressed: () => _updateContractStatus(ContractStatus.sent),
              ),
            if ((_contract!.status == ContractStatus.sent ||
                    _contract!.status == ContractStatus.draft) &&
                !_isDocumentSigned)
              ElevatedButton.icon(
                icon: const Icon(Icons.draw),
                label: const Text('Request Signature'),
                onPressed: _requestElectronicSignature,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            if (_isDocumentSigned &&
                _contract!.status != ContractStatus.signed &&
                _contract!.status != ContractStatus.completed)
              ElevatedButton.icon(
                icon: const Icon(Icons.check_circle),
                label: const Text('Mark as Signed'),
                onPressed: () => _updateContractStatus(ContractStatus.signed),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            if (_contract!.status == ContractStatus.signed)
              ElevatedButton.icon(
                icon: const Icon(Icons.done_all),
                label: const Text('Mark as Completed'),
                onPressed:
                    () => _updateContractStatus(ContractStatus.completed),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            if (_contract!.status != ContractStatus.cancelled &&
                _contract!.status != ContractStatus.completed)
              ElevatedButton.icon(
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel'),
                onPressed:
                    () => _updateContractStatus(ContractStatus.cancelled),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // Document actions
        const Text(
          'Document Actions',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Edit contract button
            ElevatedButton.icon(
              icon: const Icon(Icons.edit),
              label: const Text('Edit Contract'),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => ContractFormScreen(contract: _contract),
                  ),
                ).then((_) => _loadContractData());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),

            // Convert to invoice button
            ElevatedButton.icon(
              icon: const Icon(Icons.receipt),
              label: const Text('Create Invoice'),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => InvoiceFormScreen(
                          jobId: _contract!.jobId,
                          contractId: _contract!.id,
                        ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),

        // View signed document button if document is signed
        if (_isDocumentSigned) ...[
          const SizedBox(height: 16),
          Center(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.visibility),
              label: const Text('View Signed Document'),
              onPressed: _viewSignedDocument,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case ContractStatus.draft:
        return Colors.grey;
      case ContractStatus.sent:
        return Colors.blue;
      case ContractStatus.signed:
        return Colors.green;
      case ContractStatus.completed:
        return Colors.purple;
      case ContractStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Request electronic signature for the contract
  Future<void> _requestElectronicSignature() async {
    // Validate required data is available
    if (_contract == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot request signature: Missing contract data',
      );
      return;
    }

    // Check if user has a signature before allowing signing request
    final documentSignatureService = DocumentSignatureService();
    if (!await documentSignatureService.hasUserSignature()) {
      if (!mounted) return;

      // Use centralized feedback system
      ErrorDisplay.showWarning(
        context,
        'You must create your signature before sending contracts for signing',
      );
      return;
    }

    // Check if customer has an email
    if (_customer!.email == null || _customer!.email!.isEmpty) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot request signature: Customer has no email address',
      );
      return;
    }

    // Check if document is already signed
    if (_isDocumentSigned) {
      if (!mounted) return;

      // Show a dialog asking if they want to view the signed document or send a new request
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Document Already Signed'),
              content: const Text(
                'This contract has already been signed. Would you like to view the signed document or send a new signature request?',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _viewSignedDocument();
                  },
                  child: const Text('View Signed Document'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _generateAndSendSigningRequest();
                  },
                  child: const Text('Send New Request'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ],
            ),
      );
      return;
    }

    _generateAndSendSigningRequest();
  }

  // Generate PDF and send signing request
  Future<void> _generateAndSendSigningRequest() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _contractPdfService.generateContractPdf(
          contract: _contract!,
          customer: _customer!,
          job: _job!,
        );

        if (!mounted) return;

        // Navigate to the create signing request screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => CreateSigningRequestScreen(
                  documentType: 'contract',
                  documentId: _contract!.id,
                  documentBytes: pdfBytes,
                  customer: _customer!,
                  job: _job!,
                ),
          ),
        ).then((_) {
          // Refresh the document signed status when returning from the signing request screen
          _checkIfDocumentSigned();
        });
      }, documentType: 'Contract');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }

  // View the signed document
  Future<void> _viewSignedDocument() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Get the signed document URL
        final signedDocumentUrl = await _documentSigningService
            .getSignedDocumentUrl(_contract!.id, 'contract');

        if (signedDocumentUrl == null) {
          if (!mounted) return;
          ErrorDisplay.showWarning(context, 'Signed document not found');
          return;
        }

        // Download the signed document
        final signedDocumentBytes = await _contractPdfService.downloadPdf(
          signedDocumentUrl,
        );

        if (!mounted) return;

        // Open the PDF in the document viewer
        final contractNumber = _contract!.id.substring(0, 8);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => DocumentViewerScreen(
                  title: 'Signed Contract #$contractNumber',
                  documentBytes: signedDocumentBytes,
                ),
          ),
        );
      }, documentType: 'Contract');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error viewing signed document: ${e.toString()}',
        );
      }
    }
  }
}
