import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/screens/payments/index.dart';
import 'package:quarterlies/services/invoice_pdf_service.dart';
import 'package:quarterlies/screens/document_viewer_screen.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'invoice_form_screen.dart';

class InvoiceDetailScreen extends StatefulWidget {
  final String invoiceId;

  const InvoiceDetailScreen({super.key, required this.invoiceId});

  @override
  State<InvoiceDetailScreen> createState() => _InvoiceDetailScreenState();
}

class _InvoiceDetailScreenState extends State<InvoiceDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final InvoicePdfService _invoicePdfService = InvoicePdfService();
  Invoice? _invoice;
  Job? _job;
  Customer? _customer;
  List<Payment> _payments = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadInvoiceData();
  }

  Future<void> _loadInvoiceData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadInvoiceData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get invoice details
          final invoice = await _supabaseService.getInvoiceById(
            widget.invoiceId,
          );
          if (invoice == null) {
            setState(() {
              _errorMessage = 'Invoice not found';
            });
            return;
          }

          // Get job details
          final job = await _supabaseService.getJobById(invoice.jobId);

          // Get customer details
          final customer = await _supabaseService.getCustomerById(
            invoice.customerId,
          );

          // Get payments for this invoice
          final payments = await _supabaseService.getPaymentsByInvoice(
            widget.invoiceId,
          );

          if (!mounted) return;

          setState(() {
            _invoice = invoice;
            _job = job;
            _customer = customer;
            _payments = payments;
          });
        },
        message: 'Loading invoice data...',
        errorMessage: 'Failed to load invoice data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load invoice data: ${e.toString()}';
        });
      }
    }
  }

  /// View invoice as PDF in the app
  Future<void> _viewInvoicePdf() async {
    // Validate required data is available
    if (_invoice == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot view PDF: Missing invoice data',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _invoicePdfService.generateInvoicePdf(
          invoice: _invoice!,
          customer: _customer!,
          job: _job!,
          payments: _payments,
        );

        if (!mounted) return;

        // Show PDF generation success feedback
        ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);

        // Open the PDF in the document viewer
        final invoiceNumber = _getInvoiceNumber();
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => DocumentViewerScreen(
                  title: 'Invoice #$invoiceNumber',
                  documentBytes: pdfBytes,
                ),
          ),
        );
      }, documentType: 'Invoice');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }

  /// Generate and share invoice as PDF
  Future<void> _generateAndSharePdf() async {
    // Validate required data is available
    if (_invoice == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot generate PDF: Missing invoice data',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _invoicePdfService.generateInvoicePdf(
          invoice: _invoice!,
          customer: _customer!,
          job: _job!,
          payments: _payments,
        );

        if (!mounted) return;

        // Show PDF generation success feedback
        ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);

        // Handle sharing based on platform
        if (kIsWeb) {
          // Web platform - offer download via browser
          try {
            // Show success message for web platform
            ErrorDisplay.showOperation(
              context,
              'PDF generated successfully. Download would be available in production.',
            );
          } catch (webError) {
            if (!mounted) return;
            ErrorDisplay.showWarning(
              context,
              'Error in web environment: ${webError.toString()}',
            );
          }
        } else {
          // Mobile platforms - save file and share
          try {
            // Create a descriptive filename
            final invoiceNumber = _getInvoiceNumber();
            final customerName = _customer!.name.replaceAll(' ', '_');
            final fileName = 'invoice_${invoiceNumber}_$customerName.pdf';

            // Save PDF to temporary file
            await _invoicePdfService.savePdfToFile(pdfBytes, fileName);

            // In a real app, you would implement sharing functionality here
            // using the appropriate method based on the platform and package version

            // Show success message
            if (mounted) {
              ErrorDisplay.showOperation(context, 'PDF shared successfully');
            }
          } catch (shareError) {
            if (mounted) {
              ErrorDisplay.showWarning(
                context,
                'Error sharing PDF: ${shareError.toString()}',
              );
            }
          }
        }
      }, documentType: 'Invoice');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }

  /// Save the current invoice as a template
  Future<void> _saveAsTemplate() async {
    if (!mounted) return;

    // Show dialog to get template name
    final TextEditingController templateController = TextEditingController();
    final templateName = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Save as Template'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Enter a name for this template:'),
                const SizedBox(height: 16),
                TextField(
                  controller: templateController,
                  autofocus: true,
                  decoration: const InputDecoration(
                    labelText: 'Template Name',
                    border: OutlineInputBorder(),
                  ),
                  onSubmitted: (value) => Navigator.of(context).pop(value),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed:
                    () => Navigator.of(context).pop(templateController.text),
                child: const Text('Save'),
              ),
            ],
          ),
    );
    templateController.dispose();

    // If user cancelled, return
    if (templateName == null || templateName.isEmpty || !mounted) return;

    try {
      // Create template
      await _supabaseService.createInvoiceTemplate(_invoice!, templateName);

      if (mounted) {
        ErrorDisplay.showDataOperation(context, 'Template', 'created');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to create template: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _deleteInvoice() async {
    if (!mounted) return;

    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this invoice? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
        ) ??
        false;

    if (confirmed && mounted) {
      try {
        await _supabaseService.deleteInvoice(widget.invoiceId);
        if (mounted) {
          ErrorDisplay.showDataOperation(context, 'Invoice', 'deleted');
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ErrorDisplay.showWarning(
            context,
            'Error deleting invoice: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _invoice != null
              ? 'Invoice #${_getInvoiceNumber()}'
              : 'Invoice Details',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // View PDF button
          if (_invoice != null)
            Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isGeneratingPdf = loadingProvider.isGeneratingPdf;
                return IconButton(
                  icon:
                      isGeneratingPdf
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Icon(Icons.visibility),
                  tooltip: 'View Invoice',
                  onPressed: isGeneratingPdf ? null : _viewInvoicePdf,
                );
              },
            ),
          // PDF download/share button
          if (_invoice != null)
            Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isGeneratingPdf = loadingProvider.isGeneratingPdf;
                return IconButton(
                  icon:
                      isGeneratingPdf
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Icon(Icons.picture_as_pdf),
                  tooltip: 'Download/Share PDF',
                  onPressed: isGeneratingPdf ? null : _generateAndSharePdf,
                );
              },
            ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => InvoiceFormScreen(invoice: _invoice),
                ),
              ).then((_) => _loadInvoiceData());
            },
          ),
          // Save as template option
          IconButton(
            icon: const Icon(Icons.save_outlined),
            tooltip: 'Save as Template',
            onPressed: _invoice == null ? null : _saveAsTemplate,
          ),
          IconButton(icon: const Icon(Icons.delete), onPressed: _deleteInvoice),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadInvoiceData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading invoice data...',
                  size: 32.0,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadInvoiceData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _invoice == null
              ? const Center(child: Text('Invoice not found'))
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Invoice header section
                        AdaptiveDetailSection(
                          title: 'Invoice Details',
                          icon: Icons.receipt_long,
                          alwaysExpandedInOffice: true,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Invoice #${_getInvoiceNumber()}',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 16 : 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Chip(
                                  label: Text(_invoice!.status),
                                  backgroundColor: _getStatusColor(
                                    _invoice!.status,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: displayProvider.isOfficeMode ? 8 : 12,
                            ),

                            if (displayProvider.isOfficeMode) ...[
                              // Office Mode: Compact grid layout
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Customer',
                                      value: _customer?.name ?? 'Unknown',
                                      icon: Icons.person,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Job',
                                      value: _job?.title ?? 'Unknown',
                                      icon: Icons.work,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Issue Date',
                                      value: DateFormat(
                                        'MM/dd/yyyy',
                                      ).format(_invoice!.issueDate),
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Due Date',
                                      value: DateFormat(
                                        'MM/dd/yyyy',
                                      ).format(_invoice!.dueDate),
                                      icon: Icons.event,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Total Amount',
                                      value:
                                          '\$${_invoice!.totalAmount.toStringAsFixed(2)}',
                                      icon: Icons.attach_money,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Amount Paid',
                                      value:
                                          '\$${_invoice!.amountPaid.toStringAsFixed(2)}',
                                      icon: Icons.payment,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              AdaptiveInfoRow(
                                label: 'Balance Due',
                                value:
                                    '\$${_invoice!.balanceDue.toStringAsFixed(2)}',
                                icon: Icons.account_balance_wallet,
                              ),
                            ] else ...[
                              // Field Mode: Existing layout
                              Text('Customer: ${_customer?.name ?? 'Unknown'}'),
                              Text('Job: ${_job?.title ?? 'Unknown'}'),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Issue Date: ${DateFormat('MM/dd/yyyy').format(_invoice!.issueDate)}',
                                  ),
                                  Text(
                                    'Due Date: ${DateFormat('MM/dd/yyyy').format(_invoice!.dueDate)}',
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Total Amount: \$${_invoice!.totalAmount.toStringAsFixed(2)}',
                                  ),
                                  Text(
                                    'Amount Paid: \$${_invoice!.amountPaid.toStringAsFixed(2)}',
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Balance Due: \$${_invoice!.balanceDue.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      _invoice!.balanceDue > 0
                                          ? Colors.red
                                          : Colors.green,
                                ),
                              ),
                            ],
                          ],
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 12 : 16,
                        ),

                        // Line items section
                        AdaptiveDetailSection(
                          title: 'Line Items',
                          icon: Icons.list_alt,
                          alwaysExpandedInOffice: true,
                          children: [
                            if (_invoice!.lineItems == null ||
                                _invoice!.lineItems!.isEmpty)
                              const Text('No line items found')
                            else ...[
                              // Header row
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 6 : 8,
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Text(
                                        'Description',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 14,
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Qty',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 14,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Unit',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 14,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Price',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 14,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Total',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 14,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(),
                              // Line items
                              ..._invoice!.lineItems!.map(
                                (item) => Padding(
                                  padding: EdgeInsets.symmetric(
                                    vertical:
                                        displayProvider.isOfficeMode ? 4 : 8,
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 3,
                                        child: Text(
                                          item.description,
                                          style: TextStyle(
                                            fontSize:
                                                displayProvider.isOfficeMode
                                                    ? 12
                                                    : 14,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          item.quantity.toString(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize:
                                                displayProvider.isOfficeMode
                                                    ? 12
                                                    : 14,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          item.unit,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize:
                                                displayProvider.isOfficeMode
                                                    ? 12
                                                    : 14,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          '\$${item.unitPrice.toStringAsFixed(2)}',
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            fontSize:
                                                displayProvider.isOfficeMode
                                                    ? 12
                                                    : 14,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            fontSize:
                                                displayProvider.isOfficeMode
                                                    ? 12
                                                    : 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const Divider(),
                              // Total row
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 6 : 8,
                                ),
                                child: Row(
                                  children: [
                                    const Expanded(
                                      flex: 6,
                                      child: Text(
                                        'Total',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        '\$${_invoice!.totalAmount.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 12 : 16,
                        ),

                        // Payments section
                        AdaptiveDetailSection(
                          title: 'Payments',
                          icon: Icons.payment,
                          alwaysExpandedInOffice: true,
                          children: [
                            // Action buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Quick Pay button - only show if invoice is not fully paid
                                if (_invoice!.balanceDue > 0)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: ElevatedButton.icon(
                                      onPressed: () => _showQuickPayDialog(),
                                      icon: const Icon(Icons.payments),
                                      label: const Text('Quick Pay'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            Theme.of(
                                              context,
                                            ).colorScheme.secondary,
                                        foregroundColor: Colors.white,
                                        padding: EdgeInsets.symmetric(
                                          horizontal:
                                              displayProvider.isOfficeMode
                                                  ? 12
                                                  : 16,
                                          vertical:
                                              displayProvider.isOfficeMode
                                                  ? 8
                                                  : 12,
                                        ),
                                      ),
                                    ),
                                  ),
                                // Regular Add Payment button
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (context) => PaymentFormScreen(
                                              jobId: _invoice!.jobId,
                                              invoiceId: _invoice!.id,
                                            ),
                                      ),
                                    ).then((_) => _loadInvoiceData());
                                  },
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add Payment'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.primary,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          displayProvider.isOfficeMode
                                              ? 12
                                              : 16,
                                      vertical:
                                          displayProvider.isOfficeMode ? 8 : 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: displayProvider.isOfficeMode ? 8 : 12,
                            ),

                            // Payments list
                            if (_payments.isEmpty)
                              const Text('No payments recorded')
                            else ...[
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _payments.length,
                                itemBuilder: (context, index) {
                                  final payment = _payments[index];
                                  return ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal:
                                          displayProvider.isOfficeMode ? 8 : 16,
                                      vertical:
                                          displayProvider.isOfficeMode ? 4 : 8,
                                    ),
                                    title: Text(
                                      '\$${payment.amountReceived.toStringAsFixed(2)}',
                                      style: TextStyle(
                                        fontSize:
                                            displayProvider.isOfficeMode
                                                ? 14
                                                : 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Text(
                                      'Date: ${DateFormat('MM/dd/yyyy').format(payment.dateReceived)}\nMethod: ${payment.paymentMethod}',
                                      style: TextStyle(
                                        fontSize:
                                            displayProvider.isOfficeMode
                                                ? 12
                                                : 14,
                                      ),
                                    ),
                                    trailing: const Icon(
                                      Icons.arrow_forward_ios,
                                    ),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) => PaymentDetailScreen(
                                                paymentId: payment.id,
                                              ),
                                        ),
                                      ).then((_) => _loadInvoiceData());
                                    },
                                  );
                                },
                              ),
                              Padding(
                                padding: EdgeInsets.all(
                                  displayProvider.isOfficeMode ? 6 : 8,
                                ),
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (context) => PaymentListScreen(
                                              invoiceId: _invoice!.id,
                                            ),
                                      ),
                                    ).then((_) => _loadInvoiceData());
                                  },
                                  child: const Text('View All Payments'),
                                ),
                              ),
                            ],
                          ],
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 12 : 16,
                        ),

                        // Notes section
                        if (_invoice!.notes != null &&
                            _invoice!.notes!.isNotEmpty)
                          AdaptiveDetailSection(
                            title: 'Notes',
                            icon: Icons.note,
                            alwaysExpandedInOffice: true,
                            children: [
                              Text(
                                _invoice!.notes!,
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  );
                },
              );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'partially paid':
        return Colors.amber;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Helper method to safely get invoice number
  String _getInvoiceNumber() {
    if (_invoice == null || _invoice!.id == null) {
      return "Unknown";
    }

    return _invoice!.id!.length >= 8
        ? _invoice!.id!.substring(0, 8)
        : _invoice!.id!;
  }

  // Show dialog for quick payment with partial payment support
  Future<void> _showQuickPayDialog() async {
    if (!mounted || _invoice == null) return;

    final paymentMethods = [
      'Cash',
      'Check',
      'Credit Card',
      'Bank Transfer',
      'Other',
    ];
    String selectedMethod = 'Cash';
    bool isFullPayment = true;
    final amountController = TextEditingController(
      text: _invoice!.balanceDue.toStringAsFixed(2),
    );

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Quick Pay Invoice'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Invoice #${_getInvoiceNumber()}'),
                      const SizedBox(height: 8),
                      Text(
                        'Balance Due: \$${_invoice!.balanceDue.toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Checkbox(
                            value: isFullPayment,
                            onChanged: (value) {
                              setState(() {
                                isFullPayment = value ?? true;
                                if (isFullPayment) {
                                  amountController.text = _invoice!.balanceDue
                                      .toStringAsFixed(2);
                                }
                              });
                            },
                          ),
                          const Text('Pay full amount'),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (!isFullPayment) ...[
                        const Text('Payment Amount:'),
                        TextField(
                          controller: amountController,
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          decoration: const InputDecoration(
                            prefixText: '\$',
                            hintText: 'Enter payment amount',
                          ),
                          onChanged: (value) {
                            // Validate that amount is not greater than balance due
                            final amount = double.tryParse(value) ?? 0.0;
                            if (amount > _invoice!.balanceDue) {
                              amountController.text = _invoice!.balanceDue
                                  .toStringAsFixed(2);
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                      ],
                      const Text('Payment Method:'),
                      DropdownButton<String>(
                        value: selectedMethod,
                        isExpanded: true,
                        items:
                            paymentMethods
                                .map(
                                  (method) => DropdownMenuItem<String>(
                                    value: method,
                                    child: Text(method),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => selectedMethod = value);
                          }
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Validate amount
                        final amount =
                            double.tryParse(amountController.text) ?? 0.0;
                        if (amount <= 0) {
                          ErrorDisplay.showWarning(
                            context,
                            'Please enter a valid amount',
                          );
                          return;
                        }
                        if (amount > _invoice!.balanceDue) {
                          ErrorDisplay.showWarning(
                            context,
                            'Amount cannot exceed balance due',
                          );
                          return;
                        }

                        Navigator.pop(context, {
                          'confirmed': true,
                          'amount': amount,
                          'method': selectedMethod,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Pay Now'),
                    ),
                  ],
                ),
          ),
    );

    if (result != null && result['confirmed'] == true && mounted) {
      try {
        final double paymentAmount = result['amount'];
        final String paymentMethod = result['method'];

        // Create a new payment
        final payment = Payment(
          userId: Supabase.instance.client.auth.currentUser!.id,
          jobId: _invoice!.jobId,
          invoiceId: _invoice!.id,
          amountReceived: paymentAmount,
          paymentDate: DateTime.now(),
          paymentMethod: paymentMethod,
          notes: 'Payment for invoice #${_getInvoiceNumber()}',
        );

        // Add the payment
        await _supabaseService.addPayment(payment);

        if (!mounted) return;

        // Calculate new amount paid
        final double newAmountPaid = _invoice!.amountPaid + paymentAmount;

        // Determine new status based on payment amount
        String newStatus;
        if (newAmountPaid >= _invoice!.totalAmount) {
          newStatus = InvoiceStatus.paid;
        } else if (newAmountPaid > 0) {
          newStatus = InvoiceStatus.partiallyPaid;
        } else {
          newStatus = InvoiceStatus.open;
        }

        // Update the invoice
        final updatedInvoice = _invoice!.copyWith(
          amountPaid: newAmountPaid,
          status: newStatus,
          updatedAt: DateTime.now(),
        );

        await _supabaseService.updateInvoice(updatedInvoice);

        if (mounted) {
          ErrorDisplay.showDataOperation(context, 'Payment', 'recorded');
          _loadInvoiceData(); // Refresh the data
        }
      } catch (e) {
        if (mounted) {
          ErrorDisplay.showWarning(
            context,
            'Error recording payment: ${e.toString()}',
          );
        }
      }
    }

    // Dispose of the controller
    amountController.dispose();
  }
}
