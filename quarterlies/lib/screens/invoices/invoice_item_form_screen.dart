import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/invoice.dart'; // Import the specific invoice model
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/services/voice_recording_service.dart';

class InvoiceItemFormScreen extends StatefulWidget {
  final InvoiceItem? item;

  const InvoiceItemFormScreen({super.key, this.item});

  @override
  State<InvoiceItemFormScreen> createState() => _InvoiceItemFormScreenState();
}

class _InvoiceItemFormScreenState extends State<InvoiceItemFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _voiceRecordingService = VoiceRecordingService();

  // Form controllers
  late TextEditingController _descriptionController;
  late TextEditingController _quantityController;
  late TextEditingController _unitController;
  late TextEditingController _unitPriceController;
  late TextEditingController _taxRateController;

  bool _isRecording = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController(
      text: widget.item?.description ?? '',
    );
    _quantityController = TextEditingController(
      text: widget.item?.quantity.toString() ?? '1',
    );
    _unitController = TextEditingController(text: widget.item?.unit ?? 'each');
    _unitPriceController = TextEditingController(
      text: widget.item?.unitPrice.toString() ?? '0.00',
    );
    _taxRateController = TextEditingController(
      text: widget.item?.taxRate?.toString() ?? '0.00',
    );
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitController.dispose();
    _unitPriceController.dispose();
    _taxRateController.dispose();
    super.dispose();
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'startVoiceRecording',
        () async {
          await _voiceRecordingService.initialize();
          await _voiceRecordingService.startRecording();
          setState(() {
            _isRecording = true;
          });
        },
        message: 'Starting voice recording...',
        errorMessage: 'Failed to start recording',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to start recording: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _stopRecording() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'stopVoiceRecording',
        () async {
          final transcribedText = await _voiceRecordingService.stopRecording();
          setState(() {
            _isRecording = false;
          });

          if (transcribedText.isNotEmpty) {
            // Process the transcribed text specifically for line item information
            final extractedInfo = _voiceRecordingService
                .processLineItemVoiceInput(transcribedText);

            setState(() {
              // Apply extracted information to form fields
              if (extractedInfo.containsKey('description')) {
                _descriptionController.text = extractedInfo['description'];
              }
              if (extractedInfo.containsKey('quantity')) {
                _quantityController.text = extractedInfo['quantity'];
              }
              if (extractedInfo.containsKey('unitPrice') ||
                  extractedInfo.containsKey('price')) {
                _unitPriceController.text =
                    extractedInfo['unitPrice'] ?? extractedInfo['price'];
              }
              if (extractedInfo.containsKey('unit')) {
                _unitController.text = extractedInfo['unit'];
              }
            });
          }
        },
        message: 'Processing voice recording...',
        errorMessage: 'Failed to process recording',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to process recording: ${e.toString()}';
          _isRecording = false;
        });
      }
    }
  }

  void _saveItem() {
    if (!_formKey.currentState!.validate()) return;

    final item = InvoiceItem(
      id: widget.item?.id ?? const Uuid().v4(),
      type: 'other', // Default type
      description: _descriptionController.text.trim(),
      quantity: double.parse(_quantityController.text),
      unit: _unitController.text.trim(),
      unitPrice:
          _unitPriceController.text.isEmpty
              ? 0.0
              : double.parse(_unitPriceController.text),
      taxRate: double.tryParse(_taxRateController.text),
    );

    Navigator.pop(context, item);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.item == null ? 'Add Line Item' : 'Edit Line Item',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            toolbarHeight: displayProvider.isOfficeMode ? 56 : 64,
          ),
          body: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12 : 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description field with voice recording button
                  if (_errorMessage != null)
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: displayProvider.isOfficeMode ? 12 : 16,
                      ),
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: 'Description',
                          border: const OutlineInputBorder(),
                          hintText:
                              'Enter item description or tap mic to use voice',
                          suffixIcon:
                              _isRecording
                                  ? Container(
                                    margin: const EdgeInsets.only(right: 32),
                                    child: const Icon(
                                      Icons.mic,
                                      color: Colors.red,
                                    ),
                                  )
                                  : null,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a description';
                          }
                          return null;
                        },
                      ),
                      Positioned(
                        right: 8,
                        child: IconButton(
                          icon: Icon(
                            _isRecording ? Icons.stop : Icons.mic,
                            color:
                                _isRecording
                                    ? Colors.red
                                    : Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () {
                            if (_isRecording) {
                              _stopRecording();
                            } else {
                              _startRecording();
                            }
                          },
                          tooltip:
                              _isRecording
                                  ? 'Stop recording'
                                  : 'Record line item details',
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // Quantity and Unit fields
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _quantityController,
                          decoration: const InputDecoration(
                            labelText: 'Quantity',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            if (double.tryParse(value) == null) {
                              return 'Invalid number';
                            }
                            if (double.parse(value) <= 0) {
                              return 'Must be > 0';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _unitController,
                          decoration: const InputDecoration(
                            labelText: 'Unit',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // Unit Price and Tax Rate fields
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _unitPriceController,
                          decoration: const InputDecoration(
                            labelText: 'Unit Price (\$)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          validator: (value) {
                            // Allow empty value for price field
                            if (value == null || value.isEmpty) {
                              return null; // Empty is allowed
                            }
                            if (double.tryParse(value) == null) {
                              return 'Invalid number';
                            }
                            if (double.parse(value) < 0) {
                              return 'Must be >= 0';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _taxRateController,
                          decoration: const InputDecoration(
                            labelText: 'Tax Rate (%)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (double.tryParse(value) == null) {
                                return 'Invalid number';
                              }
                              if (double.parse(value) < 0) {
                                return 'Must be >= 0';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),

                  // Total calculation
                  Card(
                    elevation: displayProvider.isOfficeMode ? 2 : 3,
                    child: Padding(
                      padding: EdgeInsets.all(
                        displayProvider.isOfficeMode ? 12 : 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Total:',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 16 : 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '\$${_calculateTotal()}',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 16 : 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),

                  // Save button and voice input button
                  Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: displayProvider.isOfficeMode ? 44 : 50,
                          child: ElevatedButton(
                            onPressed: _saveItem,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'Save Item',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 14 : 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                      if (!_isRecording)
                        Padding(
                          padding: EdgeInsets.only(
                            left: displayProvider.isOfficeMode ? 12.0 : 16.0,
                          ),
                          child: SizedBox(
                            height: displayProvider.isOfficeMode ? 44 : 50,
                            child: ElevatedButton.icon(
                              icon: Icon(
                                Icons.mic,
                                size: displayProvider.isOfficeMode ? 18 : 20,
                              ),
                              label: Text(
                                'Voice Input',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 12 : 14,
                                ),
                              ),
                              onPressed: () => _startRecording(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.secondary,
                                foregroundColor:
                                    Theme.of(context).colorScheme.onSecondary,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _calculateTotal() {
    double quantity = double.tryParse(_quantityController.text) ?? 0;
    double unitPrice =
        _unitPriceController.text.isEmpty
            ? 0
            : (double.tryParse(_unitPriceController.text) ?? 0);
    double taxRate =
        _taxRateController.text.isEmpty
            ? 0
            : (double.tryParse(_taxRateController.text) ?? 0);

    double subtotal = quantity * unitPrice;
    double tax = subtotal * (taxRate / 100);
    double total = subtotal + tax;

    return total.toStringAsFixed(2);
  }
}
