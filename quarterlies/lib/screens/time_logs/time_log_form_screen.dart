import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/recent_data_service.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class TimeLogFormScreen extends StatefulWidget {
  final TimeLog? timeLog; // Null for new time log, non-null for editing
  final String? jobId; // Optional job ID when creating from job screen
  final String? initialJobId; // For quick-add with job selection
  final Map<String, dynamic>?
  initialDefaults; // Smart defaults from recent entries

  const TimeLogFormScreen({
    super.key,
    this.timeLog,
    this.jobId,
    this.initialJobId,
    this.initialDefaults,
  });

  @override
  State<TimeLogFormScreen> createState() => _TimeLogFormScreenState();
}

class _TimeLogFormScreenState extends State<TimeLogFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _hoursController = TextEditingController();
  final _notesController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _flatRateController = TextEditingController();
  final _dataRepository = DataRepository();
  final _recentDataService = RecentDataService();
  final _voiceRecordingService = VoiceRecordingService();

  String? _selectedJobId;
  DateTime _selectedDate = DateTime.now();
  List<Job> _jobs = [];
  bool _isFlatRate = false;
  double _calculatedLaborCost = 0.0;

  bool _isRecording = false;
  String? _errorMessage;
  String? _voiceNoteUrl;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isOffline = false;

  bool get _isEditing => widget.timeLog != null;

  @override
  void initState() {
    super.initState();
    _loadJobs();
    _setupConnectivityListener();

    if (_isEditing) {
      // Populate form fields with existing time log data
      _hoursController.text = widget.timeLog!.hours.toString();
      _notesController.text = widget.timeLog!.notes ?? '';
      _selectedDate = widget.timeLog!.date;
      _selectedJobId = widget.timeLog!.jobId;
      _isFlatRate = widget.timeLog!.isFlatRate;

      if (_isFlatRate) {
        _flatRateController.text = widget.timeLog!.laborCost.toString();
      } else {
        _hourlyRateController.text = widget.timeLog!.hourlyRate.toString();
      }

      _calculatedLaborCost = widget.timeLog!.laborCost;
    } else {
      // Apply smart defaults from recent entries
      if (widget.initialDefaults != null &&
          widget.initialDefaults!.containsKey('hourly_rate')) {
        _hourlyRateController.text =
            widget.initialDefaults!['hourly_rate'].toString();
      }

      // Set initial job ID from quick-add selection
      if (widget.initialJobId != null) {
        _selectedJobId = widget.initialJobId;
        // Update job usage tracking
        if (_selectedJobId != null) {
          _recentDataService.updateJobUsage(_selectedJobId!);
        }
      } else if (widget.jobId != null) {
        _selectedJobId = widget.jobId;
      }
    }

    // Add listeners to update calculated labor cost
    _hoursController.addListener(_updateLaborCost);
    _hourlyRateController.addListener(_updateLaborCost);
    _flatRateController.addListener(_updateLaborCost);
  }

  @override
  void dispose() {
    _hoursController.dispose();
    _notesController.dispose();
    _hourlyRateController.dispose();
    _flatRateController.dispose();
    _voiceRecordingService.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadJobs() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobs',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get jobs from DataRepository (works both online and offline)
          final jobs = await _dataRepository.getJobs();
          final isOnline = await _dataRepository.isOnline();

          setState(() {
            _jobs = jobs;
          });

          // Show connectivity status if offline
          if (!isOnline && mounted) {
            ErrorDisplay.showSync(
              context,
              'You are currently offline. Changes will be synced when connection is restored.',
              isOffline: true,
            );
          }
        },
        message: 'Loading jobs...',
        errorMessage: 'Failed to load jobs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load jobs: ${e.toString()}';
        });
      }
    }
  }

  void _updateLaborCost() {
    if (_isFlatRate) {
      setState(() {
        _calculatedLaborCost = double.tryParse(_flatRateController.text) ?? 0.0;
      });
    } else {
      final hours = double.tryParse(_hoursController.text) ?? 0.0;
      final hourlyRate = double.tryParse(_hourlyRateController.text) ?? 0.0;
      setState(() {
        _calculatedLaborCost = hours * hourlyRate;
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'startRecording'},
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Update form fields with extracted information
        setState(() {
          if (extractedInfo.containsKey('description') &&
              _notesController.text.isEmpty) {
            _notesController.text = extractedInfo['description'];
          }

          if (extractedInfo.containsKey('hours') &&
              _hoursController.text.isEmpty) {
            _hoursController.text = extractedInfo['hours'];
          }

          if (extractedInfo.containsKey('rate') &&
              !_isFlatRate &&
              _hourlyRateController.text.isEmpty) {
            _hourlyRateController.text = extractedInfo['rate'];
          } else if (extractedInfo.containsKey('amount') &&
              _isFlatRate &&
              _flatRateController.text.isEmpty) {
            _flatRateController.text = extractedInfo['amount'];
          }

          if (extractedInfo.containsKey('jobName') && _selectedJobId == null) {
            // Try to find a job with a matching name
            final jobName = extractedInfo['jobName'];
            // Find a job that matches the name
            Job? matchingJob;
            try {
              matchingJob = _jobs.firstWhere(
                (job) =>
                    job.title.toLowerCase().contains(jobName.toLowerCase()),
              );
            } catch (e) {
              // If no job matches, use the first job if available
              matchingJob = _jobs.isNotEmpty ? _jobs.first : null;
            }

            if (matchingJob != null) {
              _selectedJobId = matchingJob.id;
            }
          }
        });

        // Show voice operation feedback
        if (mounted) {
          ErrorDisplay.showOperation(
            context,
            'Voice recording processed successfully',
          );
        }

        // Upload the audio file to Supabase Storage
        final recordId = widget.timeLog?.id ?? const Uuid().v4();
        final recordingPath = _voiceRecordingService.getRecordingPath();
        if (recordingPath != null && recordingPath.isNotEmpty) {
          _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
            recordingPath,
            'time_logs',
            recordId,
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'stopRecording'},
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _saveTimeLog() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedJobId == null) {
      ErrorDisplay.showWarning(context, 'Please select a job');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveTimeLog',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final hours = double.parse(_hoursController.text);
          final notes =
              _notesController.text.isEmpty ? null : _notesController.text;

          double hourlyRate = 0.0;
          double laborCost = 0.0;

          if (_isFlatRate) {
            laborCost = double.parse(_flatRateController.text);
            // For flat rate, we still store an hourly rate for reference
            hourlyRate = hours > 0 ? laborCost / hours : 0.0;
          } else {
            hourlyRate = double.parse(_hourlyRateController.text);
            laborCost = hours * hourlyRate;
          }

          final timeLog = TimeLog(
            id: _isEditing ? widget.timeLog!.id : null,
            userId:
                _isEditing
                    ? widget.timeLog!.userId
                    : Supabase.instance.client.auth.currentUser!.id,
            jobId: _selectedJobId!,
            date: _selectedDate,
            hours: hours,
            notes: notes,
            hourlyRate: hourlyRate,
            laborCost: laborCost,
            isFlatRate: _isFlatRate,
            voiceNoteUrl: _voiceNoteUrl ?? widget.timeLog?.voiceNoteUrl,
            createdAt: _isEditing ? widget.timeLog!.createdAt : null,
            updatedAt: DateTime.now(),
          );

          // Check if we're online
          final isOnline = await _dataRepository.isOnline();

          // Add time log to DataRepository (handles both online and offline)
          await _dataRepository.addTimeLog(timeLog);

          // Update recent data tracking
          if (!_isEditing) {
            _recentDataService.updateJobUsage(_selectedJobId!);
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'time log',
              operation,
              isOffline: !isOnline,
            );
            Navigator.pop(context, true);
          }
        },
        message: _isEditing ? 'Updating time log...' : 'Creating time log...',
        errorMessage: 'Failed to save time log',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveTimeLog',
            'isEditing': _isEditing,
            'isFlatRate': _isFlatRate,
            'selectedJobId': _selectedJobId,
            'hasVoiceNote': _voiceNoteUrl != null,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Time Log' : 'Add Time Log'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Tooltip(
                message:
                    'You are currently offline. Changes will be saved locally.',
                child: const Icon(Icons.cloud_off, color: Colors.white),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<LoadingStateProvider>(
      builder: (context, loadingProvider, child) {
        if (loadingProvider.isLoading('saveTimeLog')) {
          return const Center(child: CircularProgressIndicator());
        }

        return Consumer<DisplaySettingsProvider>(
          builder: (context, displayProvider, child) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12 : 16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Offline status indicator
                    if (_isOffline)
                      Container(
                        padding: const EdgeInsets.all(8.0),
                        margin: const EdgeInsets.only(bottom: 16.0),
                        decoration: BoxDecoration(
                          color: Colors.orange.withAlpha(51), // 0.2 * 255 = 51
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(color: Colors.orange),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.cloud_off, color: Colors.orange),
                            const SizedBox(width: 8.0),
                            Expanded(
                              child: Text(
                                'You are currently offline. Your changes will be saved locally and synced when connection is restored.',
                                style: TextStyle(color: Colors.orange[800]),
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            border: Border.all(color: Colors.red.shade200),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red.shade700,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.close),
                                iconSize: 18,
                                color: Colors.red.shade700,
                                onPressed: () {
                                  setState(() {
                                    _errorMessage = null;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ),

                    // Basic Information Section
                    AdaptiveFormSection(
                      title: 'Basic Information',
                      icon: Icons.info,
                      children: [
                        if (displayProvider.isOfficeMode) ...[
                          // Office Mode: Job and Date in same row
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child:
                                    loadingProvider.isLoading('loadJobs')
                                        ? const Center(
                                          child: CircularProgressIndicator(),
                                        )
                                        : DropdownButtonFormField<String>(
                                          decoration: const InputDecoration(
                                            labelText: 'Job',
                                            border: OutlineInputBorder(),
                                          ),
                                          value: _selectedJobId,
                                          items:
                                              _jobs.map((job) {
                                                return DropdownMenuItem<String>(
                                                  value: job.id,
                                                  child: Text(job.title),
                                                );
                                              }).toList(),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'Please select a job';
                                            }
                                            return null;
                                          },
                                          onChanged: (value) {
                                            setState(() {
                                              _selectedJobId = value;
                                            });
                                          },
                                        ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: InkWell(
                                  onTap: () => _selectDate(context),
                                  child: InputDecorator(
                                    decoration: const InputDecoration(
                                      labelText: 'Date',
                                      border: OutlineInputBorder(),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          DateFormat(
                                            'MMM d, yyyy',
                                          ).format(_selectedDate),
                                        ),
                                        const Icon(
                                          Icons.calendar_today,
                                          size: 18,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ] else ...[
                          // Field Mode: Separate fields
                          loadingProvider.isLoading('loadJobs')
                              ? const Center(child: CircularProgressIndicator())
                              : DropdownButtonFormField<String>(
                                decoration: const InputDecoration(
                                  labelText: 'Job',
                                  border: OutlineInputBorder(),
                                ),
                                value: _selectedJobId,
                                items:
                                    _jobs.map((job) {
                                      return DropdownMenuItem<String>(
                                        value: job.id,
                                        child: Text(job.title),
                                      );
                                    }).toList(),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a job';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {
                                    _selectedJobId = value;
                                  });
                                },
                              ),
                          InkWell(
                            onTap: () => _selectDate(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Date',
                                border: OutlineInputBorder(),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    DateFormat(
                                      'MMM d, yyyy',
                                    ).format(_selectedDate),
                                  ),
                                  const Icon(Icons.calendar_today),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    // Time and Rate Section
                    AdaptiveFormSection(
                      title: 'Time & Rate',
                      icon: Icons.timer,
                      children: [
                        // Hours worked
                        TextFormField(
                          controller: _hoursController,
                          decoration: const InputDecoration(
                            labelText: 'Hours Worked',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          validator:
                              (value) => InputValidators.validateNumber(
                                value,
                                required: true,
                                fieldName: 'Hours worked',
                                min: 0.01,
                                max: 24.0,
                              ),
                        ),

                        // Rate type selection
                        Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 8 : 12,
                          ),
                          child: Row(
                            children: [
                              Text(
                                'Rate Type:',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 14 : 16,
                                ),
                              ),
                              const SizedBox(width: 16),
                              ChoiceChip(
                                label: Text(
                                  'Hourly Rate',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 12 : 14,
                                  ),
                                ),
                                selected: !_isFlatRate,
                                onSelected: (selected) {
                                  if (selected) {
                                    setState(() {
                                      _isFlatRate = false;
                                      _updateLaborCost();
                                    });
                                  }
                                },
                              ),
                              const SizedBox(width: 8),
                              ChoiceChip(
                                label: Text(
                                  'Flat Rate',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 12 : 14,
                                  ),
                                ),
                                selected: _isFlatRate,
                                onSelected: (selected) {
                                  if (selected) {
                                    setState(() {
                                      _isFlatRate = true;
                                      _updateLaborCost();
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                        ),

                        // Rate input based on selection
                        if (_isFlatRate)
                          TextFormField(
                            controller: _flatRateController,
                            decoration: const InputDecoration(
                              labelText: 'Flat Rate Amount (\$)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            validator:
                                (value) =>
                                    _isFlatRate
                                        ? InputValidators.validateCurrency(
                                          value,
                                          required: true,
                                          fieldName: 'Flat rate amount',
                                        )
                                        : null,
                          )
                        else
                          TextFormField(
                            controller: _hourlyRateController,
                            decoration: const InputDecoration(
                              labelText: 'Hourly Rate (\$)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            validator:
                                (value) =>
                                    !_isFlatRate
                                        ? InputValidators.validateCurrency(
                                          value,
                                          required: true,
                                          fieldName: 'Hourly rate',
                                        )
                                        : null,
                          ),
                      ],
                    ),

                    // Calculated labor cost display
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total Labor Cost:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '\$${_calculatedLaborCost.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Notes with voice recording button
                    TextFormField(
                      controller: _notesController,
                      decoration: InputDecoration(
                        labelText: 'Notes (Optional)',
                        border: const OutlineInputBorder(),
                        suffixIcon:
                            _isRecording
                                ? const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: CircularProgressIndicator(),
                                )
                                : IconButton(
                                  icon: Icon(
                                    _isRecording ? Icons.stop : Icons.mic,
                                    color:
                                        _isRecording
                                            ? Colors.red
                                            : Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                  ),
                                  onPressed:
                                      _isRecording
                                          ? _stopRecording
                                          : _startRecording,
                                  tooltip:
                                      _isRecording
                                          ? 'Stop recording'
                                          : 'Record voice note',
                                ),
                      ),
                      maxLines: 3,
                    ),
                    if (_isRecording)
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          'Recording... Speak clearly to capture notes, hours, and rate information.',
                          style: TextStyle(
                            color: Colors.red,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    const SizedBox(height: 24),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveTimeLog,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _isEditing ? 'Update Time Log' : 'Add Time Log',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
